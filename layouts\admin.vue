<template>
  <div class="flex h-screen bg-gray-50 dark:bg-gray-900">
    <!-- Admin Sidebar -->
    <aside
      class="fixed inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0"
      :class="{ '-translate-x-full': !isMobileSidebarOpen }">
      <!-- Sidebar Header -->
      <div class="flex items-center justify-between h-16 px-6 border-b border-gray-200 dark:border-gray-700">
        <div class="flex items-center space-x-2">
          <Icon name="heroicons:building-office" class="h-8 w-8 text-blue-600" />
          <span class="text-lg font-semibold text-gray-900 dark:text-white">Admin Panel</span>
        </div>
        <button @click="closeMobileSidebar"
          class="lg:hidden text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
          <Icon name="heroicons:x-mark" class="h-6 w-6" />
        </button>
      </div>

      <!-- Navigation -->
      <nav class="mt-6 px-3">
        <div class="space-y-1">
          <!-- Dashboard -->
          <NuxtLink to="/admin/dashboard"
            class="flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors" :class="isActiveRoute('/admin/dashboard')
              ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-200'
              : 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700'">
            <Icon name="heroicons:home" class="h-5 w-5 mr-3" />
            Dashboard
          </NuxtLink>

          <!-- Schools Management -->
          <NuxtLink to="/admin/schools"
            class="flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors" :class="isActiveRoute('/admin/schools')
              ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-200'
              : 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700'">
            <Icon name="heroicons:building-office-2" class="h-5 w-5 mr-3" />
            My Schools
          </NuxtLink>

          <!-- Teachers Management -->
          <NuxtLink to="/admin/teachers"
            class="flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors" :class="isActiveRoute('/admin/teachers')
              ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-200'
              : 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700'">
            <Icon name="heroicons:users" class="h-5 w-5 mr-3" />
            Teachers
          </NuxtLink>

          <!-- Billing & Subscription -->
          <NuxtLink to="/admin/billing"
            class="flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors" :class="isActiveRoute('/admin/billing')
              ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-200'
              : 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700'">
            <Icon name="heroicons:credit-card" class="h-5 w-5 mr-3" />
            Billing
          </NuxtLink>

          <!-- Coupons Management (Super Admin) -->
          <NuxtLink v-if="isSuperAdmin" to="/admin/coupons"
            class="flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors" :class="isActiveRoute('/admin/coupons')
              ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-200'
              : 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700'">
            <Icon name="heroicons:ticket" class="h-5 w-5 mr-3" />
            Coupons
          </NuxtLink>

          <!-- Analytics -->
          <NuxtLink to="/admin/analytics"
            class="flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors" :class="isActiveRoute('/admin/analytics')
              ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-200'
              : 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700'">
            <Icon name="heroicons:chart-bar" class="h-5 w-5 mr-3" />
            Analytics
          </NuxtLink>

          <!-- Settings -->
          <NuxtLink to="/admin/settings"
            class="flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors" :class="isActiveRoute('/admin/settings')
              ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-200'
              : 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700'">
            <Icon name="heroicons:cog-6-tooth" class="h-5 w-5 mr-3" />
            Settings
          </NuxtLink>
        </div>

        <!-- Divider -->
        <div class="border-t border-gray-200 dark:border-gray-700 my-6"></div>

        <!-- School Access -->
        <div class="space-y-1">
          <p class="px-3 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider">
            School Access
          </p>
          <div v-if="userSchools.length > 0" class="space-y-1">
            <NuxtLink v-for="school in userSchools" :key="school.id"
              :to="`//${school.code}.${$config.public.baseDomain}`" target="_blank"
              class="flex items-center px-3 py-2 text-sm font-medium rounded-lg text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700 transition-colors">
              <Icon name="heroicons:arrow-top-right-on-square" class="h-4 w-4 mr-3" />
              {{ school.name }}
            </NuxtLink>
          </div>
          <div v-else class="px-3 py-2 text-sm text-gray-500 dark:text-gray-400">
            No schools registered
          </div>
        </div>
      </nav>

      <!-- User Menu -->
      <div class="absolute bottom-0 left-0 right-0 p-4 border-t border-gray-200 dark:border-gray-700">
        <div class="flex items-center space-x-3">
          <div class="flex-shrink-0">
            <img v-if="user?.user_metadata?.avatar_url" :src="user.user_metadata.avatar_url" :alt="user.email"
              class="h-8 w-8 rounded-full">
            <div v-else class="h-8 w-8 rounded-full bg-blue-600 flex items-center justify-center">
              <span class="text-sm font-medium text-white">
                {{ user?.email?.charAt(0).toUpperCase() }}
              </span>
            </div>
          </div>
          <div class="flex-1 min-w-0">
            <p class="text-sm font-medium text-gray-900 dark:text-white truncate">
              {{ user?.user_metadata?.full_name || user?.email }}
            </p>
            <p class="text-xs text-gray-500 dark:text-gray-400 truncate">
              Admin
            </p>
          </div>
          <button @click="handleLogout"
            class="flex-shrink-0 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            title="Logout">
            <Icon name="heroicons:arrow-right-on-rectangle" class="h-5 w-5" />
          </button>
        </div>
      </div>
    </aside>

    <!-- Mobile Sidebar Overlay -->
    <div v-if="isMobileSidebarOpen" class="fixed inset-0 z-40 bg-gray-600 bg-opacity-75 lg:hidden"
      @click="closeMobileSidebar"></div>

    <!-- Main Content Area -->
    <div class="flex-1 flex flex-col overflow-hidden lg:ml-0">
      <!-- Header -->
      <header class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between h-16 px-4 sm:px-6 lg:px-8">
          <!-- Mobile menu button -->
          <button @click="toggleMobileSidebar"
            class="lg:hidden text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
            <Icon name="heroicons:bars-3" class="h-6 w-6" />
          </button>

          <!-- Page Title -->
          <div class="flex-1 lg:flex-none">
            <h1 class="text-lg font-semibold text-gray-900 dark:text-white">
              {{ pageTitle }}
            </h1>
          </div>

          <!-- Header Actions -->
          <div class="flex items-center space-x-4">
            <!-- Theme Switcher -->
            <UiBaseThemeSwitcher />

            <!-- Notifications -->
            <button class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
              <Icon name="heroicons:bell" class="h-6 w-6" />
            </button>
          </div>
        </div>
      </header>

      <!-- Main Content -->
      <main class="flex-1 overflow-x-hidden overflow-y-auto p-4 sm:p-6 lg:p-8">
        <slot />
      </main>
    </div>

    <!-- Toast Container -->
    <ClientOnly>
      <UiToastContainer />
    </ClientOnly>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import type { School } from '~/types/multiTenant'

// Mobile sidebar state
const isMobileSidebarOpen = ref(false)

// User and auth
const user = useSupabaseUser()
const supabaseClient = useSupabaseClient()
const router = useRouter()
const route = useRoute()

// Page title
const pageTitle = computed(() => {
  const routeName = route.name as string
  if (routeName?.includes('admin-dashboard')) return 'Dashboard'
  if (routeName?.includes('admin-schools')) return 'Schools'
  if (routeName?.includes('admin-teachers')) return 'Teachers'
  if (routeName?.includes('admin-billing')) return 'Billing'
  if (routeName?.includes('admin-coupons')) return 'Coupons'
  if (routeName?.includes('admin-analytics')) return 'Analytics'
  if (routeName?.includes('admin-settings')) return 'Settings'
  return 'Admin Panel'
})

// Check if user is super admin (placeholder - implement based on your logic)
const isSuperAdmin = computed(() => {
  // TODO: Implement super admin check
  return user.value?.email === '<EMAIL>'
})

// User schools (placeholder - implement with actual data fetching)
const userSchools = ref<School[]>([])

// Navigation helpers
const toggleMobileSidebar = () => {
  isMobileSidebarOpen.value = !isMobileSidebarOpen.value
}

const closeMobileSidebar = () => {
  isMobileSidebarOpen.value = false
}

const isActiveRoute = (path: string) => {
  return route.path.startsWith(path)
}

// Logout handler
const handleLogout = async () => {
  closeMobileSidebar()

  const { error } = await supabaseClient.auth.signOut()
  if (error) {
    console.error('Error logging out:', error.message)
  } else {
    await router.push('/admin/login')
  }
}

// Close mobile sidebar on route change
watch(() => route.path, () => {
  closeMobileSidebar()
})
</script>

<style scoped>
/* Custom styles for admin layout */
</style>
