<template>
  <div class="space-y-6">
    <!-- Welcome Header -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
            Welcome back, {{ user?.user_metadata?.full_name || user?.email }}
          </h1>
          <p class="text-gray-600 dark:text-gray-400">
            Manage your schools and monitor your educational platform
          </p>
        </div>
        <div class="flex items-center space-x-4">
          <NuxtLink to="/admin/coupons"
            class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
            Manage Coupons
          </NuxtLink>
          <NuxtLink to="/admin/analytics"
            class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
            View Analytics
          </NuxtLink>
        </div>
      </div>
    </div>

    <!-- Stats Overview -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <!-- Total Schools -->
      <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <Icon name="heroicons:building-office-2" class="h-8 w-8 text-blue-600" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Schools</p>
            <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ stats.totalSchools }}</p>
          </div>
        </div>
      </div>

      <!-- Active Teachers -->
      <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <Icon name="heroicons:users" class="h-8 w-8 text-green-600" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Active Teachers</p>
            <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ stats.activeTeachers }}</p>
          </div>
        </div>
      </div>

      <!-- Monthly Revenue -->
      <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <Icon name="heroicons:currency-dollar" class="h-8 w-8 text-yellow-600" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Monthly Revenue</p>
            <p class="text-2xl font-semibold text-gray-900 dark:text-white">${{ stats.monthlyRevenue }}</p>
          </div>
        </div>
      </div>

      <!-- Active Subscriptions -->
      <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <Icon name="heroicons:check-circle" class="h-8 w-8 text-purple-600" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Active Subscriptions</p>
            <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ stats.activeSubscriptions }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Recent Schools -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
      <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between">
          <h2 class="text-lg font-medium text-gray-900 dark:text-white">Your Schools</h2>
          <NuxtLink to="/admin/schools" class="text-blue-600 hover:text-blue-500 text-sm font-medium">
            View all
          </NuxtLink>
        </div>
      </div>

      <div class="p-6">
        <div v-if="isLoadingSchools" class="space-y-4">
          <div v-for="i in 3" :key="i" class="animate-pulse">
            <div class="flex items-center space-x-4">
              <div class="h-12 w-12 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
              <div class="flex-1 space-y-2">
                <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4"></div>
                <div class="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
              </div>
              <div class="h-8 w-20 bg-gray-200 dark:bg-gray-700 rounded"></div>
            </div>
          </div>
        </div>

        <div v-else-if="schools.length === 0" class="text-center py-8">
          <Icon name="heroicons:building-office-2" class="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No schools yet</h3>
          <p class="text-gray-600 dark:text-gray-400 mb-4">
            Get started by registering your first school
          </p>
          <NuxtLink to="/admin/register"
            class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
            Register School
          </NuxtLink>
        </div>

        <div v-else class="space-y-4">
          <div v-for="school in schools.slice(0, 5)" :key="school.id"
            class="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
            <div class="flex items-center space-x-4">
              <div class="h-12 w-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                <Icon name="heroicons:building-office" class="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <h3 class="text-sm font-medium text-gray-900 dark:text-white">{{ school.name }}</h3>
                <p class="text-sm text-gray-600 dark:text-gray-400">{{ school.code }}</p>
              </div>
            </div>
            <div class="flex items-center space-x-3">
              <span :class="[
                'px-2 py-1 text-xs font-medium rounded-full',
                school.subscription_status === 'active'
                  ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                  : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
              ]">
                {{ school.subscription_status }}
              </span>
              <a :href="`//${school.code}.${$config.public.baseDomain}`" target="_blank"
                class="text-blue-600 hover:text-blue-500 text-sm font-medium">
                Visit School
                <Icon name="heroicons:arrow-top-right-on-square" class="h-4 w-4 inline ml-1" />
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <!-- Manage Teachers -->
      <NuxtLink to="/admin/teachers"
        class="bg-white dark:bg-gray-800 shadow rounded-lg p-6 hover:shadow-md transition-shadow">
        <div class="flex items-center">
          <Icon name="heroicons:users" class="h-8 w-8 text-blue-600 mr-4" />
          <div>
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">Manage Teachers</h3>
            <p class="text-gray-600 dark:text-gray-400">Add, remove, and manage teacher access</p>
          </div>
        </div>
      </NuxtLink>

      <!-- Billing & Subscriptions -->
      <NuxtLink to="/admin/billing"
        class="bg-white dark:bg-gray-800 shadow rounded-lg p-6 hover:shadow-md transition-shadow">
        <div class="flex items-center">
          <Icon name="heroicons:credit-card" class="h-8 w-8 text-green-600 mr-4" />
          <div>
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">Billing</h3>
            <p class="text-gray-600 dark:text-gray-400">Manage subscriptions and payments</p>
          </div>
        </div>
      </NuxtLink>

      <!-- Analytics -->
      <NuxtLink to="/admin/analytics"
        class="bg-white dark:bg-gray-800 shadow rounded-lg p-6 hover:shadow-md transition-shadow">
        <div class="flex items-center">
          <Icon name="heroicons:chart-bar" class="h-8 w-8 text-purple-600 mr-4" />
          <div>
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">Analytics</h3>
            <p class="text-gray-600 dark:text-gray-400">View usage and performance metrics</p>
          </div>
        </div>
      </NuxtLink>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import type { School } from '~/types/multiTenant'

// Use admin layout
definePageMeta({
  layout: 'admin' as any,
  middleware: 'admin-auth' as any
})

// State
const user = useSupabaseUser()
const schools = ref<School[]>([])
const isLoadingSchools = ref(true)

const stats = ref({
  totalSchools: 0,
  activeTeachers: 0,
  monthlyRevenue: 0,
  activeSubscriptions: 0
})

// Methods
const fetchDashboardData = async () => {
  try {
    // TODO: Implement actual API calls to fetch dashboard data
    // For now, using mock data

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000))

    // Mock data
    schools.value = [
      {
        id: '1',
        name: 'Springfield Elementary',
        code: 'SPE001',
        subscription_status: 'active',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        admin_user_id: user.value?.id || '',
        subscription_expires_at: null,
        description: null,
        contact_email: null,
        contact_phone: null,
        address: null,
        settings: {}
      }
    ]

    stats.value = {
      totalSchools: 3,
      activeTeachers: 25,
      monthlyRevenue: 1250,
      activeSubscriptions: 3
    }

  } catch (error) {
    console.error('Error fetching dashboard data:', error)
  } finally {
    isLoadingSchools.value = false
  }
}

// Lifecycle
onMounted(() => {
  fetchDashboardData()
})

// SEO
useHead({
  title: 'Admin Dashboard - RPHMate',
  meta: [
    {
      name: 'description',
      content: 'Manage your schools and monitor your educational platform.'
    }
  ]
})
</script>

<style scoped>
/* Custom styles for admin dashboard */
</style>
